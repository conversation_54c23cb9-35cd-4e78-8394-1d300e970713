<template>
  <div class="mod-config">
    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.name" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-input v-model="dataForm.mobile" placeholder="手机号" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.channelId" placeholder="所属渠道" clearable>
          <el-option v-for="channel in channelList" :key="channel.id" :label="channel.name" :value="channel.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button type="primary" @click="addOrUpdateHandle()">新增业务员</el-button>
        <el-button type="warning" @click="inviteSalesmanHandle()">邀请业务员</el-button>
        <el-button type="danger" @click="deleteHandle()" :disabled="dataListSelections.length <= 0">批量删除</el-button>
        <el-button @click="exportStats()">导出统计</el-button>
        <el-button type="success" @click="showSalesmanQrcode()">业务员页面</el-button>
        <el-button type="success" @click=" $router.push({ name: 'salesman-commission-config' })">提成配置</el-button>
        
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <!-- 总体统计卡片 -->
    <div class="stats-overview" style="margin-bottom: 20px;">
      <el-row :gutter="20">
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.totalSalesmen || 0 }}</div>
              <div class="stats-label">活跃业务员</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">{{ overallStats.totalOrders || 0 }}</div>
              <div class="stats-label">总订单数</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ overallStats.totalAmount || 0 }}</div>
              <div class="stats-label">总销售额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="5">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ overallStats.totalPayAmount || 0 }}</div>
              <div class="stats-label">已收款金额</div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="4">
          <el-card class="stats-card">
            <div class="stats-item">
              <div class="stats-value">¥{{ overallStats.totalCommission || 0 }}</div>
              <div class="stats-label">总佣金</div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle" style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="name" header-align="center" align="center" label="业务员姓名">
      </el-table-column>
      <el-table-column prop="mobile" header-align="center" align="center" label="手机号">
      </el-table-column>
      <el-table-column prop="code" header-align="center" align="center" width="120" label="业务员编号">
      </el-table-column>
      <el-table-column prop="channelName" header-align="center" align="center" label="所属渠道">
      </el-table-column>
      <el-table-column prop="department" header-align="center" align="center" width="120" label="部门">
      </el-table-column>
      <el-table-column prop="position" header-align="center" align="center" width="100" label="职位">
      </el-table-column>
      <el-table-column prop="status" header-align="center" align="center" width="80" label="状态">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.status === 0" size="small" type="danger">禁用</el-tag>
          <el-tag v-else size="small" type="success">启用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="tags" header-align="center" align="center" width="150" label="标签">
        <template slot-scope="scope">
          <span v-if="scope.row.tags">
            <el-tag v-for="tag in scope.row.tags.split(',')" :key="tag" size="mini" style="margin-right: 5px;">
              {{ tag.trim() }}
            </el-tag>
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="parentName" header-align="center" align="center" width="120" label="上级业务员">
        <template slot-scope="scope">
          {{ scope.row.parentName || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="level" header-align="center" align="center" width="80" label="层级">
        <template slot-scope="scope">
          <el-tag size="mini" :type="getLevelType(scope.row.level)">
            L{{ scope.row.level || 1 }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="childrenCount" header-align="center" align="center" width="100" label="下级数量">
        <template slot-scope="scope">
          <div style="display: inline-block; position: relative;">
            <el-tag size="mini" :type="scope.row.childrenCount > 0 ? 'success' : 'info'">
              {{ scope.row.childrenCount || 0 }} 人
            </el-tag>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="totalOrders" header-align="center" align="center" width="100" label="总订单数">
        <template slot-scope="scope">
          <el-tag type="primary">{{ scope.row.totalOrders || 0 }}</el-tag>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="activityOrders" header-align="center" align="center" label="活动订单">
        <template slot-scope="scope">
          <el-tag type="success">{{ scope.row.activityOrders || 0 }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="rechargeOrders" header-align="center" align="center" label="充值订单">
        <template slot-scope="scope">
          <el-tag type="warning">{{ scope.row.rechargeOrders || 0 }}</el-tag>
        </template>
      </el-table-column> -->
      <el-table-column prop="totalAmount" header-align="center" align="center" label="销售总额">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.totalAmount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalPayAmount" header-align="center" align="center" label="已收金额">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.totalPayAmount || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="totalCommission" header-align="center" align="center" label="佣金总额">
        <template slot-scope="scope">
          <span style="color: #67C23A; font-weight: bold;">¥{{ scope.row.totalCommission || 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="420" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="addOrUpdateHandle(scope.row.id)">修改</el-button>
          <!-- <el-button type="text" size="small" @click="qrcodeHandle(scope.row.id)">二维码</el-button> -->
          <el-button type="text" size="small" @click="viewOrdersHandle(scope.row.id, scope.row.name)">订单</el-button>
          <!-- <el-button type="text" size="small" @click="viewDetailsHandle(scope.row.id, scope.row.name)">统计</el-button> -->
          <el-button type="text" size="small" @click="commissionConfigHandle(scope.row.id, scope.row.name)">佣金配置</el-button>
          <el-button type="text" size="small" @click="viewBindingCustomersHandle(scope.row.id, scope.row.name)">绑定客户</el-button>
          <el-button type="text" size="small" @click="deleteHandle(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 详细统计弹窗 -->
    <el-dialog title="业务员详细统计" :visible.sync="detailsDialogVisible" width="800px">
      <div v-if="selectedSalesmanStats">
        <h4>{{ selectedSalesmanName }} - 业绩详情</h4>

        <!-- 统计卡片 -->
        <el-row :gutter="20" style="margin-bottom: 20px;">
          <el-col :span="8">
            <el-card class="detail-stats-card">
              <div class="stats-item">
                <div class="stats-value">{{ selectedSalesmanStats.totalOrders || 0 }}</div>
                <div class="stats-label">总订单数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="detail-stats-card">
              <div class="stats-item">
                <div class="stats-value">¥{{ selectedSalesmanStats.totalAmount || 0 }}</div>
                <div class="stats-label">销售总额</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="8">
            <el-card class="detail-stats-card">
              <div class="stats-item">
                <div class="stats-value">¥{{ selectedSalesmanStats.totalCommission || 0 }}</div>
                <div class="stats-label">佣金总额</div>
              </div>
            </el-card>
          </el-col>
        </el-row>

        <!-- 订单类型分布 -->
        <el-row :gutter="20">
          <el-col :span="12">
            <el-card>
              <div slot="header">订单类型分布</div>
              <div class="order-type-stats">
                <div class="order-type-item">
                  <span>活动订单：</span>
                  <el-tag type="success">{{ selectedSalesmanStats.activityOrders || 0 }}</el-tag>
                </div>
                <div class="order-type-item">
                  <span>充值订单：</span>
                  <el-tag type="warning">{{ selectedSalesmanStats.rechargeOrders || 0 }}</el-tag>
                </div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card>
              <div slot="header">佣金信息</div>
              <div class="commission-stats">
                <div class="commission-item">
                  <span>平均佣金率：</span>
                  <span style="color: #409EFF;">{{ getAverageCommissionRate() }}%</span>
                </div>
                <div class="commission-item">
                  <span>单笔平均佣金：</span>
                  <span style="color: #67C23A;">¥{{ getAverageCommission() }}</span>
                </div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="detailsDialogVisible = false">确定</el-button>
      </span>
    </el-dialog>

    <!-- 新增修改业务员弹窗 -->
    <add-or-update v-if="addOrUpdateVisible" ref="addOrUpdate" @refreshDataList="getDataList"></add-or-update>

    <!-- 二维码管理弹窗 -->
    <qrcode-manage v-if="qrcodeManageVisible" ref="qrcodeManage"></qrcode-manage>

    <!-- 绑定客户弹窗 -->
    <binding-customers v-if="bindingCustomersVisible" ref="bindingCustomers"></binding-customers>
    
    <!-- 业务员页面二维码弹窗 -->
    <el-dialog title="业务员页面二维码" :visible.sync="salesmanQrcodeVisible" width="450px" center>
      <div style="text-align: center;">
        <canvas ref="qrcodeCanvas" width="300" height="350"
          style="border: 1px solid #ddd; border-radius: 8px;"></canvas>
        <p style="margin-top: 20px; color: #666;">扫描二维码访问业务员页面</p>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="copyQrcodeImage">复制图片</el-button>
        <el-button type="primary" @click="salesmanQrcodeVisible = false">确定</el-button>
      </span>
    </el-dialog>

    <!-- 邀请业务员弹窗 -->
    <el-dialog title="邀请业务员" :visible.sync="inviteDialogVisible" width="500px" center>
      <!-- 渠道选择 -->
      <div v-if="channelList.length > 1" style="margin-bottom: 20px;">
        <el-form label-width="80px">
          <el-form-item label="选择渠道：">
            <el-select v-model="selectedChannelId" placeholder="请选择渠道" style="width: 100%;">
              <el-option v-for="channel in channelList" :key="channel.id" :label="channel.name" :value="channel.id">
              </el-option>
            </el-select>
          </el-form-item>
        </el-form>
      </div>

      <!-- 二维码图片显示 -->
      <div v-if="inviteQrcodeUrl" style="text-align: center;">
        <canvas ref="inviteQrcodeCanvas" width="300" height="380"
          style="border: 1px solid #ddd; border-radius: 8px; background: white;"></canvas>
        <p style="margin-top: 20px; color: #666;">扫描二维码邀请业务员注册</p>

        <!-- 邀请链接 -->
        <div style="margin-top: 20px;">
          <el-input v-model="inviteQrcodeUrl" readonly>
            <el-button slot="append" @click="copyInviteLink">复制链接</el-button>
          </el-input>
        </div>
      </div>

      <!-- 提示信息 -->
      <div v-else-if="channelList.length > 1 && !selectedChannelId" style="text-align: center; color: #999; padding: 40px;">
        <i class="el-icon-info" style="font-size: 24px; margin-bottom: 10px; display: block;"></i>
        <p>请先选择渠道生成邀请二维码</p>
      </div>

      <span slot="footer" class="dialog-footer">
        <el-button @click="copyInviteQrcodeImage" v-if="inviteQrcodeUrl">复制图片</el-button>
        <el-button type="primary" @click="inviteDialogVisible = false">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import AddOrUpdate from './salesman-add-or-update'
import QrcodeManage from './salesman-qrcode-manage'
import BindingCustomers from './salesman-binding-customers'
import VueQrcode from '@chenfengyuan/vue-qrcode'

export default {
  components: {
    AddOrUpdate,
    QrcodeManage,
    BindingCustomers,
    VueQrcode
  },
  data() {
    return {
      channelList: [],
      salesmanQrcodeVisible: false,
      inviteDialogVisible: false,
      selectedChannelId: null,
      selectedChannelName: '',
      inviteQrcodeUrl: '',
      dataForm: {
        name: '',
        channelId: '',
        mobile: ''
      },
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      overallStats: {},
      detailsDialogVisible: false,
      selectedSalesmanStats: null,
      selectedSalesmanName: '',
      addOrUpdateVisible: false,
      qrcodeManageVisible: false,
      bindingCustomersVisible: false,
      selectedSalesmanId: null,
      salesmanPageUrl: 'https://yqihua.com/p_front/#/salesman/qrcode'
    }
  },
  activated() {
    this.getDataList()
    this.getOverallStats()
    this.getChannelList()
  },
  watch: {
    // 监听渠道选择变化
    selectedChannelId(newVal) {
      if (newVal) {
        this.generateInviteQrcode()
      } else {
        this.inviteQrcodeUrl = ''
      }
    }
  },
  methods: {
    
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true
      this.$http({
        url: this.$http.adornUrl('/salesman/salesman/listWithStats'),
        method: 'get',
        params: this.$http.adornParams({
          'page': this.pageIndex,
          'limit': this.pageSize,
          'name': this.dataForm.name,
          'channelId': this.dataForm.channelId,
          'mobile': this.dataForm.mobile
        })
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list || []
          this.totalPage = this.dataList.length
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取总体统计
    getOverallStats() {
      this.$http({
        url: this.$http.adornUrl('/salesman/salesman/orderStats'),
        params: this.$http.adornParams(
          {
          'name': this.dataForm.name,
          'channelId': this.dataForm.channelId,
          'mobile': this.dataForm.mobile
          }
        ),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.overallStats = data.stats
        }
      })
    },
    // 获取渠道列表
    getChannelList() {
      this.$http({
        url: this.$http.adornUrl('/channel/channel/select'),
        method: 'get',
        params: this.$http.adornParams()
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.channelList = data.channelList || []
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 查看订单
    viewOrdersHandle(salesmanId, salesmanName) {
      this.$router.push({
        name: 'salesman-order',
        query: {
          salesmanId: salesmanId,
          salesmanName: salesmanName
        }
      })
    },
    // 查看详细统计
    viewDetailsHandle(salesmanId, salesmanName) {
      this.selectedSalesmanName = salesmanName
      this.$http({
        url: this.$http.adornUrl(`/salesman/order/statsBySalesman/${salesmanId}`),
        method: 'get'
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.selectedSalesmanStats = data.stats
          this.detailsDialogVisible = true
        }
      })
    },
    // 计算平均佣金率
    getAverageCommissionRate() {
      if (!this.selectedSalesmanStats || !this.selectedSalesmanStats.totalOrders || this.selectedSalesmanStats.totalOrders === 0) {
        return '0.00'
      }
      const rate = (this.selectedSalesmanStats.totalCommission / this.selectedSalesmanStats.totalAmount) * 100
      return rate.toFixed(2)
    },
    // 计算单笔平均佣金
    getAverageCommission() {
      if (!this.selectedSalesmanStats || !this.selectedSalesmanStats.totalOrders || this.selectedSalesmanStats.totalOrders === 0) {
        return '0.00'
      }
      const avg = this.selectedSalesmanStats.totalCommission / this.selectedSalesmanStats.totalOrders
      return avg.toFixed(2)
    },
    // 新增 / 修改
    addOrUpdateHandle(id) {
      this.addOrUpdateVisible = true
      this.$nextTick(() => {
        this.$refs.addOrUpdate.init(id)
      })
    },
    // 二维码管理
    qrcodeHandle(id) {
      this.qrcodeManageVisible = true
      this.$nextTick(() => {
        this.$refs.qrcodeManage.init(id)
      })
    },
    // 删除
    deleteHandle(id) {
      var ids = id ? [id] : this.dataListSelections.map(item => {
        return item.id
      })
      this.$confirm(`确定对[id=${ids.join(',')}]进行[${id ? '删除' : '批量删除'}]操作?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$http({
          url: this.$http.adornUrl('/salesman/salesman/delete'),
          method: 'post',
          data: this.$http.adornData(ids, false)
        }).then(({data}) => {
          if (data && data.code === 200) {
            this.$message({
              message: '操作成功',
              type: 'success',
              duration: 1500,
              onClose: () => {
                this.getDataList()
              }
            })
          } else {
            this.$message.error(data.msg)
          }
        })
      })
    },
    // 获取层级类型
    getLevelType(level) {
      switch (level) {
        case 1:
          return 'success'  // 一级：绿色
        case 2:
          return 'primary'  // 二级：蓝色
        case 3:
          return 'warning'  // 三级：橙色
        default:
          return 'info'     // 其他：灰色
      }
    },
    // 导出统计
    exportStats() {
      this.$message.info('导出功能开发中...')
    },
    
    // 显示业务员页面二维码
    showSalesmanQrcode() {
      this.salesmanQrcodeVisible = true
      this.$nextTick(() => {
        this.generateQrcodeCanvas()
      })
    },
    // 生成canvas二维码
    async generateQrcodeCanvas() {
      const canvas = this.$refs.qrcodeCanvas
      const ctx = canvas.getContext('2d')

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 设置背景色为白色
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      try {
        // 使用本地qrcode库生成二维码
        const QRCode = require('qrcode')

        // 生成二维码数据URL
        const qrcodeDataURL = await QRCode.toDataURL(this.salesmanPageUrl, {
          width: 200,
          height: 200,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })

        // 创建二维码图片
        const qrcodeImg = new Image()
        qrcodeImg.onload = () => {
          // 绘制二维码，居中显示
          const qrcodeSize = 200
          const qrcodeX = (canvas.width - qrcodeSize) / 2
          const qrcodeY = 30
          ctx.drawImage(qrcodeImg, qrcodeX, qrcodeY, qrcodeSize, qrcodeSize)

          // 绘制文字
          ctx.fillStyle = '#333333'
          ctx.font = 'bold 16px Arial, sans-serif'
          ctx.textAlign = 'center'
          ctx.textBaseline = 'middle'

          const textY = qrcodeY + qrcodeSize + 40
          ctx.fillText('扫描二维码访问业务员页面', canvas.width / 2, textY)
        }

        qrcodeImg.onerror = () => {
          this.drawErrorMessage(ctx, canvas, '二维码图片加载失败')
        }

        qrcodeImg.src = qrcodeDataURL
      } catch (error) {
        console.error('生成二维码失败:', error)
        this.drawErrorMessage(ctx, canvas, '二维码生成失败')
      }
    },

    // 绘制错误信息
    drawErrorMessage(ctx, canvas, message) {
      ctx.fillStyle = '#ff0000'
      ctx.font = '16px Arial'
      ctx.textAlign = 'center'
      ctx.textBaseline = 'middle'
      ctx.fillText(message, canvas.width / 2, canvas.height / 2)
    },
    // 复制二维码图片
    copyQrcodeImage() {
      const canvas = this.$refs.qrcodeCanvas

      // 将canvas转换为blob
      canvas.toBlob((blob) => {
        if (navigator.clipboard && window.ClipboardItem) {
          // 使用现代剪贴板API
          const item = new ClipboardItem({ 'image/png': blob })
          navigator.clipboard.write([item]).then(() => {
            this.$message.success('图片已复制到剪贴板')
          }).catch(() => {
            this.fallbackCopyImage(canvas)
          })
        } else {
          this.fallbackCopyImage(canvas)
        }
      }, 'image/png')
    },
    // 备用复制方法
    fallbackCopyImage(canvas) {
      try {
        // 创建一个临时的图片元素
        const dataURL = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.download = '易企化业务员二维码.png'
        link.href = dataURL
        link.click()
        this.$message.success('图片已下载到本地')
      } catch (error) {
        this.$message.error('复制失败，请手动截图保存')
      }
    },
    // 佣金配置
    commissionConfigHandle(salesmanId, salesmanName) {
      this.$router.push({
        path: '/salesman-commission-config',
        query: {
          salesmanId: salesmanId + '',
          salesmanName: salesmanName
        }
      })
    },

    // 查看绑定客户
    viewBindingCustomersHandle(id, name) {
      this.selectedSalesmanId = id
      this.selectedSalesmanName = name
      this.bindingCustomersVisible = true
      this.$nextTick(() => {
        this.$refs.bindingCustomers.init(id, name)
      })
    },

    // 邀请业务员
    inviteSalesmanHandle() {
      if (this.channelList.length === 0) {
        this.$message.error('没有可用的渠道')
        return
      }

      if (this.channelList.length === 1) {
        // 只有一个渠道，直接生成二维码
        this.selectedChannelId = this.channelList[0].id
        this.generateInviteQrcode()
      } else {
        // 多个渠道，显示选择弹窗
        this.selectedChannelId = null
        this.inviteQrcodeUrl = ''
      }

      this.inviteDialogVisible = true
    },

    // 生成邀请二维码
    generateInviteQrcode() {
      if (!this.selectedChannelId) {
        return
      }

      // 获取渠道名称
      const channel = this.channelList.find(c => c.id === this.selectedChannelId)
      this.selectedChannelName = channel ? channel.name : ''

      // 生成邀请链接
      const baseUrl = 'https://yqihua.com/p_front/#/salesman/register'
      this.inviteQrcodeUrl = `${baseUrl}?channelId=${this.selectedChannelId}`

      // 生成canvas二维码图片
      this.$nextTick(() => {
        this.generateInviteQrcodeCanvas()
      })
    },



    // 复制邀请链接
    copyInviteLink() {
      if (navigator.clipboard) {
        navigator.clipboard.writeText(this.inviteQrcodeUrl).then(() => {
          this.$message.success('邀请链接已复制到剪贴板')
        }).catch(() => {
          this.fallbackCopyText(this.inviteQrcodeUrl)
        })
      } else {
        this.fallbackCopyText(this.inviteQrcodeUrl)
      }
    },



    // 备用文本复制方法
    fallbackCopyText(text) {
      try {
        const textArea = document.createElement('textarea')
        textArea.value = text
        document.body.appendChild(textArea)
        textArea.select()
        document.execCommand('copy')
        document.body.removeChild(textArea)
        this.$message.success('邀请链接已复制到剪贴板')
      } catch (error) {
        this.$message.error('复制失败，请手动复制链接')
      }
    },

    // 生成邀请二维码canvas图片
    async generateInviteQrcodeCanvas() {
      const canvas = this.$refs.inviteQrcodeCanvas
      if (!canvas) return

      const ctx = canvas.getContext('2d')

      // 清空画布
      ctx.clearRect(0, 0, canvas.width, canvas.height)

      // 设置背景色为白色
      ctx.fillStyle = '#ffffff'
      ctx.fillRect(0, 0, canvas.width, canvas.height)

      try {
        // 使用本地qrcode库生成二维码
        const QRCode = require('qrcode')

        // 生成二维码数据URL
        const qrcodeDataURL = await QRCode.toDataURL(this.inviteQrcodeUrl, {
          width: 200,
          height: 200,
          margin: 1,
          color: {
            dark: '#000000',
            light: '#FFFFFF'
          }
        })

        // 创建二维码图片
        const qrcodeImg = new Image()
        qrcodeImg.onload = () => {
          // 绘制二维码，居中显示
          const qrcodeSize = 200
          const qrcodeX = (canvas.width - qrcodeSize) / 2
          const qrcodeY = 40
          ctx.drawImage(qrcodeImg, qrcodeX, qrcodeY, qrcodeSize, qrcodeSize)

          // 绘制标题文字
          ctx.fillStyle = '#333333'
          ctx.font = 'bold 18px Arial, sans-serif'
          ctx.textAlign = 'center'
          ctx.textBaseline = 'middle'
          ctx.fillText('扫描二维码邀请业务员', canvas.width / 2, qrcodeY + qrcodeSize + 40)

          // 绘制渠道信息
          if (this.selectedChannelName) {
            ctx.fillStyle = '#666666'
            ctx.font = '14px Arial, sans-serif'
            ctx.fillText(`渠道：${this.selectedChannelName}`, canvas.width / 2, qrcodeY + qrcodeSize + 70)
          }
        }

        qrcodeImg.onerror = () => {
          this.drawErrorMessage(ctx, canvas, '二维码图片加载失败')
        }

        qrcodeImg.src = qrcodeDataURL
      } catch (error) {
        console.error('生成邀请二维码失败:', error)
        this.drawErrorMessage(ctx, canvas, '二维码生成失败')
      }
    },

    // 复制邀请二维码图片
    copyInviteQrcodeImage() {
      const canvas = this.$refs.inviteQrcodeCanvas
      if (!canvas) return

      // 将canvas转换为blob
      canvas.toBlob((blob) => {
        if (navigator.clipboard && window.ClipboardItem) {
          // 使用现代剪贴板API
          const item = new ClipboardItem({ 'image/png': blob })
          navigator.clipboard.write([item]).then(() => {
            this.$message.success('邀请二维码图片已复制到剪贴板')
          }).catch(() => {
            this.fallbackCopyInviteImage(canvas)
          })
        } else {
          this.fallbackCopyInviteImage(canvas)
        }
      }, 'image/png')
    },

    // 备用邀请二维码图片复制方法
    fallbackCopyInviteImage(canvas) {
      try {
        // 创建一个临时的图片元素
        const dataURL = canvas.toDataURL('image/png')
        const link = document.createElement('a')
        link.download = `邀请业务员二维码-${this.selectedChannelName || '默认渠道'}.png`
        link.href = dataURL
        link.click()
        this.$message.success('邀请二维码图片已下载到本地')
      } catch (error) {
        this.$message.error('复制失败，请手动截图保存')
      }
    }
  }
}
</script>

<style scoped>
.stats-overview {
  margin-bottom: 20px;
}

.stats-card,
.detail-stats-card {
  text-align: center;
}

.stats-item {
  padding: 20px;
}

.stats-value {
  font-size: 24px;
  font-weight: bold;
  color: #409EFF;
  margin-bottom: 8px;
}

.stats-label {
  font-size: 14px;
  color: #666;
}

.order-type-stats,
.commission-stats {
  padding: 10px 0;
}

.order-type-item,
.commission-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.order-type-item:last-child,
.commission-item:last-child {
  margin-bottom: 0;
}
</style>
