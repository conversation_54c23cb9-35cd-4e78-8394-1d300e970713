<template>
  <div class="mod-commission-record">
    <!-- 统计卡片 -->
    <el-row :gutter="20" style="margin-bottom: 20px;">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">{{ stats.totalRecords || 0 }}</div>
            <div class="stats-label">总记录数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.totalAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">总佣金金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.unsettledAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">待结算金额</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-item">
            <div class="stats-value">¥{{ (stats.settledAmount || 0).toFixed(2) }}</div>
            <div class="stats-label">已结算金额</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-form :inline="true" :model="dataForm" @keyup.enter.native="getDataList()">
      <el-form-item>
        <el-input v-model="dataForm.salesmanName" placeholder="业务员姓名" clearable></el-input>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.commissionType" placeholder="佣金类型" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="创建活动佣金" :value="1"></el-option>
          <el-option label="充值次数佣金" :value="2"></el-option>
          <el-option label="用户转发佣金" :value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-select v-model="dataForm.settlementStatus" placeholder="结算状态" clearable>
          <el-option label="全部" value=""></el-option>
          <el-option label="未结算" :value="0"></el-option>
          <el-option label="已结算" :value="1"></el-option>
          <el-option label="已取消" :value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
          end-placeholder="结束日期" format="yyyy-MM-dd" value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button @click="getDataList()">查询</el-button>
        <el-button @click="getStats()">刷新统计</el-button>
        <el-button type="primary" @click="exportData()">导出</el-button>
      </el-form-item>
      <el-form-item style="float: right">
        <el-button type="success" @click="$router.go(-1)">返回</el-button>
      </el-form-item>
    </el-form>

    <el-table :data="dataList" border v-loading="dataListLoading" @selection-change="selectionChangeHandle"
      style="width: 100%;">
      <el-table-column type="selection" header-align="center" align="center" width="50">
      </el-table-column>
      <el-table-column prop="salesmanName" header-align="center" align="center" label="业务员">
      </el-table-column>
      <el-table-column prop="commissionTypeDesc" header-align="center" align="center" label="佣金类型">
      </el-table-column>
      <el-table-column prop="commissionAmount" header-align="center" align="center" label="佣金金额">
        <template slot-scope="scope">
          ¥{{ scope.row.commissionAmount.toFixed(2) }}
        </template>
      </el-table-column>
      <el-table-column prop="orderAmount" header-align="center" align="center" label="订单金额">
        <template slot-scope="scope">
          <span v-if="scope.row.orderAmount">¥{{ scope.row.orderAmount.toFixed(2) }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="commissionRate" header-align="center" align="center" label="佣金比例">
        <template slot-scope="scope">
          <span v-if="scope.row.commissionRate">{{ (scope.row.commissionRate * 100).toFixed(2) }}%</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="settlementStatusDesc" header-align="center" align="center" label="结算状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.settlementStatus)">
            {{ scope.row.settlementStatusDesc }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="settlementBatchNo" header-align="center" align="center" label="结算批次">
        <template slot-scope="scope">
          <span v-if="scope.row.settlementBatchNo">{{ scope.row.settlementBatchNo }}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="businessTime" header-align="center" align="center" width="150" label="业务时间">
      </el-table-column>
      <el-table-column fixed="right" header-align="center" align="center" width="100" label="操作">
        <template slot-scope="scope">
          <el-button type="text" size="small" @click="viewDetails(scope.row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination @size-change="sizeChangeHandle" @current-change="currentChangeHandle" :current-page="pageIndex"
      :page-sizes="[10, 20, 50, 100]" :page-size="pageSize" :total="totalPage"
      layout="total, sizes, prev, pager, next, jumper">
    </el-pagination>

    <!-- 详情弹窗 -->
    <el-dialog title="佣金记录详情" :visible.sync="detailsDialogVisible" width="60%">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="业务员">{{ selectedRecord.salesmanName }}</el-descriptions-item>
        <el-descriptions-item label="业务员编号">{{ selectedRecord.salesmanCode }}</el-descriptions-item>
        <el-descriptions-item label="佣金类型">{{ selectedRecord.commissionTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="计算方式">{{ selectedRecord.calculationTypeDesc }}</el-descriptions-item>
        <el-descriptions-item label="佣金金额">¥{{ (selectedRecord.commissionAmount || 0).toFixed(2)
          }}</el-descriptions-item>
        <el-descriptions-item label="订单金额">
          <span v-if="selectedRecord.orderAmount">¥{{ selectedRecord.orderAmount.toFixed(2) }}</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="佣金比例">
          <span v-if="selectedRecord.commissionRate">{{ (selectedRecord.commissionRate * 100).toFixed(2) }}%</span>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item label="结算状态">
          <el-tag :type="getStatusTagType(selectedRecord.settlementStatus)">
            {{ selectedRecord.settlementStatusDesc }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="结算批次">{{ selectedRecord.settlementBatchNo || '-' }}</el-descriptions-item>
        <el-descriptions-item label="结算时间">{{ selectedRecord.settlementTime || '-' }}</el-descriptions-item>
        <el-descriptions-item label="业务时间">{{ selectedRecord.businessTime }}</el-descriptions-item>
        <el-descriptions-item label="创建时间">{{ selectedRecord.createOn }}</el-descriptions-item>
        <el-descriptions-item label="用户">{{ selectedRecord.userName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="活动">{{ selectedRecord.activityName || '-' }}</el-descriptions-item>
        <el-descriptions-item label="描述" :span="2">{{ selectedRecord.description }}</el-descriptions-item>
        <el-descriptions-item label="结算备注" :span="2">{{ selectedRecord.settlementRemarks || '-'
          }}</el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
export default {
  data() {
    return {
      dataForm: {
        salesmanName: '',
        commissionType: '',
        settlementStatus: ''
      },
      dateRange: [],
      dataList: [],
      pageIndex: 1,
      pageSize: 10,
      totalPage: 0,
      dataListLoading: false,
      dataListSelections: [],
      stats: {},
      detailsDialogVisible: false,
      selectedRecord: {}
    }
  },
  activated() {
    this.getDataList()
    this.getStats()
  },
  methods: {
    // 获取数据列表
    getDataList() {
      this.dataListLoading = true

      let params = {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'salesmanName': this.dataForm.salesmanName,
        'commissionType': this.dataForm.commissionType,
        'settlementStatus': this.dataForm.settlementStatus
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/record/list'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.dataList = data.page.list
          this.totalPage = data.page.totalCount
        } else {
          this.dataList = []
          this.totalPage = 0
        }
        this.dataListLoading = false
      })
    },
    // 获取统计数据
    getStats() {
      let params = {
        'salesmanName': this.dataForm.salesmanName,
        'commissionType': this.dataForm.commissionType,
        'settlementStatus': this.dataForm.settlementStatus
      }

      if (this.dateRange && this.dateRange.length === 2) {
        params.startTime = this.dateRange[0]
        params.endTime = this.dateRange[1]
      }

      this.$http({
        url: this.$http.adornUrl('/salesman/commission/record/stats'),
        method: 'get',
        params: this.$http.adornParams(params)
      }).then(({ data }) => {
        if (data && data.code === 200) {
          this.stats = data.stats
        }
      })
    },
    // 每页数
    sizeChangeHandle(val) {
      this.pageSize = val
      this.pageIndex = 1
      this.getDataList()
    },
    // 当前页
    currentChangeHandle(val) {
      this.pageIndex = val
      this.getDataList()
    },
    // 多选
    selectionChangeHandle(val) {
      this.dataListSelections = val
    },
    // 查看详情
    viewDetails(row) {
      this.selectedRecord = row
      this.detailsDialogVisible = true
    },
    // 获取状态标签类型
    getStatusTagType(status) {
      switch (status) {
        case 0: return 'warning'  // 未结算
        case 1: return 'success'  // 已结算
        case 2: return 'danger'   // 已取消
        default: return 'info'
      }
    },
    // 导出数据
    exportData() {
      // TODO: 实现数据导出功能
      this.$message.info('导出功能开发中...')
    }
  }
}
</script>

<style scoped>
.stats-card {
  text-align: center;
}

.stats-item {
  padding: 20px;
}

.stats-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}
</style>
