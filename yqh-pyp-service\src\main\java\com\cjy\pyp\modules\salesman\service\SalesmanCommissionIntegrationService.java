package com.cjy.pyp.modules.salesman.service;

import com.cjy.pyp.modules.activity.entity.ActivityRechargePackageEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeRecordEntity;
import com.cjy.pyp.modules.activity.entity.ActivityRechargeUsageEntity;
import com.cjy.pyp.modules.activity.service.ActivityRechargePackageService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeRecordService;
import com.cjy.pyp.modules.activity.service.ActivityRechargeUsageService;
import com.cjy.pyp.modules.salesman.entity.SalesmanCommissionRecordEntity;
import com.cjy.pyp.modules.salesman.enums.CommissionTypeEnum;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionRecordService;
import com.cjy.pyp.modules.salesman.service.SalesmanService;
import com.cjy.pyp.modules.salesman.service.OrderSalesmanAssociationService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionSafetyService;
import com.cjy.pyp.modules.salesman.service.SalesmanCommissionService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;

/**
 * 业务员佣金业务集成服务
 * 用于在相关业务流程中触发佣金计算
 * 
 * <AUTHOR>
 * @email <EMAIL>
 * @date 2025-07-16
 */
@Service
public class SalesmanCommissionIntegrationService {
    
    private static final Logger logger = LoggerFactory.getLogger(SalesmanCommissionIntegrationService.class);
    
    @Autowired
    private SalesmanCommissionRecordService commissionRecordService;
    
    @Autowired
    private ActivityRechargeRecordService rechargeRecordService;
    
    @Autowired
    private ActivityRechargePackageService rechargePackageService;
    
    @Autowired
    private ActivityRechargeUsageService rechargeUsageService;
    
    @Autowired
    private SalesmanService salesmanService;

    @Autowired
    private OrderSalesmanAssociationService orderSalesmanAssociationService;

    @Autowired
    private SalesmanCommissionSafetyService commissionSafetyService;

    @Autowired
    private SalesmanCommissionService salesmanCommissionService;

    /**
     * 处理充值完成后的佣金计算
     * 在充值记录状态更新为成功时调用
     *
     * @param rechargeRecordId 充值记录ID
     * @param appid 应用ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleRechargeCompleted(Long rechargeRecordId, String appid) {
        try {
            // 获取充值记录
            ActivityRechargeRecordEntity rechargeRecord = rechargeRecordService.getById(rechargeRecordId);
            if (rechargeRecord == null) {
                logger.warn("充值记录不存在: {}", rechargeRecordId);
                return;
            }

            // 使用新的佣金计算服务处理订单佣金
            salesmanCommissionService.processOrderCommission(rechargeRecord);

        } catch (Exception e) {
            logger.error("处理充值完成佣金计算失败: rechargeRecordId={}, appid={}", rechargeRecordId, appid, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 处理转发完成后的佣金计算
     * 在转发操作完成时调用
     *
     * @param userId 用户ID
     * @param activityId 活动ID
     * @param appid 应用ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleForwardCompleted(Long userId, Long activityId, String appid) {
        try {
            // 使用新的佣金计算服务处理转发佣金
            salesmanCommissionService.processForwardCommission(userId, activityId, appid);

        } catch (Exception e) {
            logger.error("处理转发完成佣金计算失败: userId={}, activityId={}, appid={}", userId, activityId, appid, e);
            // 不抛出异常，避免影响主业务流程
        }
    }

    /**
     * 处理用户转发后的佣金计算
     * 在用户转发记录创建时调用
     *
     * @param usageRecordId 使用记录ID
     * @param appid 应用ID
     */
    @Transactional(rollbackFor = Exception.class)
    public void handleUserForward(Long usageRecordId, String appid) {
        try {
            // 获取使用记录
            ActivityRechargeUsageEntity usageRecord = rechargeUsageService.getById(usageRecordId);
            if (usageRecord == null) {
                logger.warn("使用记录不存在: {}", usageRecordId);
                return;
            }
            
            // 只处理转发类型的使用记录 (usage_type = 3)
            if (usageRecord.getUsageType() != 3) {
                logger.info("使用记录类型不是转发，跳过佣金计算: usageType={}", usageRecord.getUsageType());
                return;
            }
            
            // 查找关联的业务员
            Long salesmanId = findSalesmanByUser(usageRecord.getUserId(), appid);
            if (salesmanId == null) {
                logger.info("用户{}没有关联业务员，跳过佣金计算", usageRecord.getUserId());
                return;
            }
            
            // 生成用户转发佣金（使用安全服务）
            commissionSafetyService.safeGenerateUserForwardCommission(salesmanId, usageRecordId, appid);
            
        } catch (Exception e) {
            logger.error("处理用户转发佣金计算失败: usageRecordId={}, appid={}", usageRecordId, appid, e);
            throw e;
        }
    }

    /**
     * 生成创建活动佣金
     */
    private void generateCreateActivityCommission(Long salesmanId, Long rechargeRecordId, BigDecimal orderAmount, String appid) {
        try {
            SalesmanCommissionRecordEntity record = commissionRecordService.generateCreateActivityCommission(
                    salesmanId, rechargeRecordId, orderAmount, appid);
            
            if (record != null) {
                logger.info("生成创建活动佣金成功: salesmanId={}, rechargeRecordId={}, amount={}", 
                           salesmanId, rechargeRecordId, record.getCommissionAmount());
            } else {
                logger.info("创建活动佣金生成跳过: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId);
            }
        } catch (Exception e) {
            logger.error("生成创建活动佣金失败: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId, e);
        }
    }

    /**
     * 生成充值次数佣金
     */
    private void generateRechargeCountCommission(Long salesmanId, Long rechargeRecordId, BigDecimal orderAmount, String appid) {
        try {
            SalesmanCommissionRecordEntity record = commissionRecordService.generateRechargeCountCommission(
                    salesmanId, rechargeRecordId, orderAmount, appid);
            
            if (record != null) {
                logger.info("生成充值次数佣金成功: salesmanId={}, rechargeRecordId={}, amount={}", 
                           salesmanId, rechargeRecordId, record.getCommissionAmount());
            } else {
                logger.info("充值次数佣金生成跳过: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId);
            }
        } catch (Exception e) {
            logger.error("生成充值次数佣金失败: salesmanId={}, rechargeRecordId={}", salesmanId, rechargeRecordId, e);
        }
    }

    /**
     * 生成用户转发佣金
     */
    private void generateUserForwardCommission(Long salesmanId, Long usageRecordId, String appid) {
        try {
            SalesmanCommissionRecordEntity record = commissionRecordService.generateUserForwardCommission(
                    salesmanId, usageRecordId, appid);
            
            if (record != null) {
                logger.info("生成用户转发佣金成功: salesmanId={}, usageRecordId={}, amount={}", 
                           salesmanId, usageRecordId, record.getCommissionAmount());
            } else {
                logger.info("用户转发佣金生成跳过: salesmanId={}, usageRecordId={}", salesmanId, usageRecordId);
            }
        } catch (Exception e) {
            logger.error("生成用户转发佣金失败: salesmanId={}, usageRecordId={}", salesmanId, usageRecordId, e);
        }
    }

    /**
     * 根据用户ID查找关联的业务员
     * 这里需要根据实际的业务逻辑来实现用户与业务员的关联关系
     * 
     * @param userId 用户ID
     * @param appid 应用ID
     * @return 业务员ID，如果没有关联则返回null
     */
    private Long findSalesmanByUser(Long userId, String appid) {
        return orderSalesmanAssociationService.findSalesmanByUser(userId, appid);
    }

    /**
     * 批量处理历史数据的佣金计算
     * 用于系统上线后补算历史佣金
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param appid 应用ID
     * @return 处理结果统计
     */
    @Transactional(rollbackFor = Exception.class)
    public String batchProcessHistoricalCommissions(String startTime, String endTime, String appid) {
        int processedRechargeCount = 0;
        int processedUsageCount = 0;
        int generatedCommissionCount = 0;
        
        try {
            // 处理历史充值记录
            // TODO: 实现历史充值记录的批量处理逻辑
            
            // 处理历史转发记录
            // TODO: 实现历史转发记录的批量处理逻辑
            
            String result = String.format("批量处理完成: 处理充值记录%d条, 处理转发记录%d条, 生成佣金记录%d条", 
                                         processedRechargeCount, processedUsageCount, generatedCommissionCount);
            logger.info(result);
            return result;
            
        } catch (Exception e) {
            logger.error("批量处理历史佣金计算失败: startTime={}, endTime={}, appid={}", startTime, endTime, appid, e);
            throw e;
        }
    }
}
