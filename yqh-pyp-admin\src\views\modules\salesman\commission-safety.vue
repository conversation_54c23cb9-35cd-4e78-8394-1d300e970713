<template>
  <div class="mod-config">
    <el-tabs v-model="activeTab" type="card">
      <!-- 防重复检查 -->
      <el-tab-pane label="防重复检查" name="duplicate">
        <div class="tab-content">
          <el-form :inline="true" :model="duplicateForm" label-width="120px">
            <el-form-item label="业务类型">
              <el-select v-model="duplicateForm.businessType" placeholder="请选择">
                <el-option label="创建活动" value="CREATE_ACTIVITY"></el-option>
                <el-option label="充值次数" value="RECHARGE_COUNT"></el-option>
                <el-option label="用户转发" value="USER_FORWARD"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="业务ID">
              <el-input v-model="duplicateForm.businessId" placeholder="请输入业务ID"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="checkDuplicate()">检查重复</el-button>
              <el-button @click="generateUniqueKey()">生成唯一标识</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="duplicateResult" class="result-panel">
            <el-alert
              :title="duplicateResult.exists ? '佣金记录已存在' : '佣金记录不存在'"
              :type="duplicateResult.exists ? 'warning' : 'success'"
              :closable="false">
            </el-alert>
            <div v-if="duplicateResult.uniqueKey" class="unique-key">
              <p><strong>唯一标识：</strong>{{ duplicateResult.uniqueKey }}</p>
            </div>
          </div>
        </div>
      </el-tab-pane>

      <!-- 失败重试 -->
      <el-tab-pane label="失败重试" name="retry">
        <div class="tab-content">
          <div class="operation-buttons">
            <el-button type="primary" @click="retryFailedCommissions()">重试失败佣金</el-button>
            <el-button type="success" @click="getFailureList()">查看失败记录</el-button>
            <el-button type="warning" @click="clearFailureRecords()">清理失败记录</el-button>
          </div>
          
          <!-- 失败记录列表 -->
          <el-table
            :data="failureList"
            border
            v-loading="failureLoading"
            style="width: 100%; margin-top: 20px;">
            <el-table-column
              prop="businessType"
              header-align="center"
              align="center"
              label="业务类型">
            </el-table-column>
            <el-table-column
              prop="businessId"
              header-align="center"
              align="center"
              label="业务ID">
            </el-table-column>
            <el-table-column
              prop="salesmanName"
              header-align="center"
              align="center"
              label="业务员">
            </el-table-column>
            <el-table-column
              prop="failureReason"
              header-align="center"
              align="center"
              label="失败原因">
            </el-table-column>
            <el-table-column
              prop="retryCount"
              header-align="center"
              align="center"
              label="重试次数">
            </el-table-column>
            <el-table-column
              prop="createOn"
              header-align="center"
              align="center"
              width="150"
              label="失败时间">
            </el-table-column>
            <el-table-column
              prop="status"
              header-align="center"
              align="center"
              label="状态">
              <template slot-scope="scope">
                <el-tag :type="getFailureStatusTagType(scope.row.status)">
                  {{ getFailureStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column
              fixed="right"
              header-align="center"
              align="center"
              width="120"
              label="操作">
              <template slot-scope="scope">
                <el-button v-if="scope.row.status === 0" type="text" size="small" @click="retryFailure(scope.row)">重试</el-button>
                <el-button type="text" size="small" @click="ignoreFailure(scope.row)">忽略</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 数据一致性 -->
      <el-tab-pane label="数据一致性" name="consistency">
        <div class="tab-content">
          <div class="operation-buttons">
            <el-button type="primary" @click="batchValidateConsistency()">批量验证一致性</el-button>
            <el-button type="success" @click="getInconsistentList()">查看不一致记录</el-button>
            <el-button type="warning" @click="batchRepairInconsistent()">批量修复</el-button>
          </div>
          
          <!-- 单个验证 -->
          <el-form :inline="true" :model="consistencyForm" style="margin-top: 20px;">
            <el-form-item label="佣金记录ID">
              <el-input v-model="consistencyForm.commissionRecordId" placeholder="请输入佣金记录ID"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button @click="validateSingleConsistency()">验证单个记录</el-button>
            </el-form-item>
          </el-form>
          
          <!-- 不一致记录列表 -->
          <el-table
            :data="inconsistentList"
            border
            v-loading="consistencyLoading"
            style="width: 100%; margin-top: 20px;">
            <el-table-column
              prop="commissionRecordId"
              header-align="center"
              align="center"
              label="佣金记录ID">
            </el-table-column>
            <el-table-column
              prop="businessType"
              header-align="center"
              align="center"
              label="业务类型">
            </el-table-column>
            <el-table-column
              prop="businessId"
              header-align="center"
              align="center"
              label="业务ID">
            </el-table-column>
            <el-table-column
              prop="salesmanName"
              header-align="center"
              align="center"
              label="业务员">
            </el-table-column>
            <el-table-column
              prop="inconsistencyReason"
              header-align="center"
              align="center"
              label="不一致原因">
            </el-table-column>
            <el-table-column
              prop="detectTime"
              header-align="center"
              align="center"
              width="150"
              label="检测时间">
            </el-table-column>
            <el-table-column
              fixed="right"
              header-align="center"
              align="center"
              width="120"
              label="操作">
              <template slot-scope="scope">
                <el-button type="text" size="small" @click="repairInconsistent(scope.row)">修复</el-button>
                <el-button type="text" size="small" @click="viewDetails(scope.row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-tab-pane>

      <!-- 锁管理 -->
      <el-tab-pane label="锁管理" name="lock">
        <div class="tab-content">
          <el-form :inline="true" :model="lockForm" label-width="120px">
            <el-form-item label="业务类型">
              <el-select v-model="lockForm.businessType" placeholder="请选择">
                <el-option label="创建活动" value="CREATE_ACTIVITY"></el-option>
                <el-option label="充值次数" value="RECHARGE_COUNT"></el-option>
                <el-option label="用户转发" value="USER_FORWARD"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="业务ID">
              <el-input v-model="lockForm.businessId" placeholder="请输入业务ID"></el-input>
            </el-form-item>
            <el-form-item label="业务员ID">
              <el-input v-model="lockForm.salesmanId" placeholder="请输入业务员ID"></el-input>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="checkLockStatus()">查看锁状态</el-button>
              <el-button type="danger" @click="releaseLock()">释放锁</el-button>
              <el-button type="warning" @click="cleanupExpiredData()">清理过期数据</el-button>
            </el-form-item>
          </el-form>
          
          <div v-if="lockResult" class="result-panel">
            <el-alert
              :title="lockResult.message"
              :type="lockResult.type"
              :closable="false">
              <template v-if="lockResult.lockKey" slot="description">
                <p><strong>锁键：</strong>{{ lockResult.lockKey }}</p>
              </template>
            </el-alert>
          </div>
        </div>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
export default {
  data () {
    return {
      activeTab: 'duplicate',
      duplicateForm: {
        businessType: '',
        businessId: ''
      },
      duplicateResult: null,
      failureList: [],
      failureLoading: false,
      consistencyForm: {
        commissionRecordId: ''
      },
      inconsistentList: [],
      consistencyLoading: false,
      lockForm: {
        businessType: '',
        businessId: '',
        salesmanId: ''
      },
      lockResult: null
    }
  },
  methods: {
    // 检查重复
    checkDuplicate () {
      if (!this.duplicateForm.businessType || !this.duplicateForm.businessId) {
        this.$message.warning('请填写完整信息')
        return
      }
      
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/checkExists'),
        method: 'get',
        params: this.$http.adornParams({
          businessType: this.duplicateForm.businessType,
          businessId: this.duplicateForm.businessId
        })
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.duplicateResult = {
            exists: data.exists
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 生成唯一标识
    generateUniqueKey () {
      if (!this.duplicateForm.businessType || !this.duplicateForm.businessId) {
        this.$message.warning('请填写完整信息')
        return
      }
      
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/generateUniqueKey'),
        method: 'get',
        params: this.$http.adornParams({
          businessType: this.duplicateForm.businessType,
          businessId: this.duplicateForm.businessId,
          salesmanId: 1 // 示例业务员ID
        })
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.duplicateResult = {
            ...this.duplicateResult,
            uniqueKey: data.uniqueKey
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 重试失败佣金
    retryFailedCommissions () {
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/retryFailed'),
        method: 'post'
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.$message.success(data.message)
          this.getFailureList()
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 获取失败记录列表
    getFailureList () {
      this.failureLoading = true
      // 模拟数据，实际应该调用API
      setTimeout(() => {
        this.failureList = []
        this.failureLoading = false
      }, 1000)
    },
    
    // 清理失败记录
    clearFailureRecords () {
      this.$confirm('确定要清理所有失败记录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('清理完成')
        this.getFailureList()
      })
    },
    
    // 批量验证一致性
    batchValidateConsistency () {
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/batchValidate'),
        method: 'post'
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.$message.success(data.message)
          this.getInconsistentList()
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 获取不一致记录列表
    getInconsistentList () {
      this.consistencyLoading = true
      // 模拟数据，实际应该调用API
      setTimeout(() => {
        this.inconsistentList = []
        this.consistencyLoading = false
      }, 1000)
    },
    
    // 验证单个记录一致性
    validateSingleConsistency () {
      if (!this.consistencyForm.commissionRecordId) {
        this.$message.warning('请输入佣金记录ID')
        return
      }
      
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/validateConsistency'),
        method: 'get',
        params: this.$http.adornParams({
          commissionRecordId: this.consistencyForm.commissionRecordId
        })
      }).then(({data}) => {
        if (data && data.code ===200) {
          if (data.consistent) {
            this.$message.success(data.message)
          } else {
            this.$message.warning(data.message)
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 查看锁状态
    checkLockStatus () {
      if (!this.lockForm.businessType || !this.lockForm.businessId || !this.lockForm.salesmanId) {
        this.$message.warning('请填写完整信息')
        return
      }
      
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/lockStatus'),
        method: 'get',
        params: this.$http.adornParams({
          businessType: this.lockForm.businessType,
          businessId: this.lockForm.businessId,
          salesmanId: this.lockForm.salesmanId
        })
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.lockResult = {
            message: data.message,
            type: 'info',
            lockKey: data.lockKey
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 释放锁
    releaseLock () {
      if (!this.lockForm.businessType || !this.lockForm.businessId || !this.lockForm.salesmanId) {
        this.$message.warning('请填写完整信息')
        return
      }
      
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/releaseLock'),
        method: 'post',
        params: this.$http.adornParams({
          businessType: this.lockForm.businessType,
          businessId: this.lockForm.businessId,
          salesmanId: this.lockForm.salesmanId
        })
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.$message.success(data.message)
          this.lockResult = {
            message: '锁已释放',
            type: 'success'
          }
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 清理过期数据
    cleanupExpiredData () {
      this.$http({
        url: this.$http.adornUrl('/salesman/commissionsafety/cleanup'),
        method: 'post'
      }).then(({data}) => {
        if (data && data.code ===200) {
          this.$message.success(data.message)
        } else {
          this.$message.error(data.msg)
        }
      })
    },
    
    // 获取失败状态文本
    getFailureStatusText (status) {
      const statusMap = {
        0: '待重试',
        1: '重试成功',
        2: '重试失败',
        3: '已忽略'
      }
      return statusMap[status] || '未知'
    },
    
    // 获取失败状态标签类型
    getFailureStatusTagType (status) {
      const typeMap = {
        0: 'warning',
        1: 'success',
        2: 'danger',
        3: 'info'
      }
      return typeMap[status] || ''
    }
  }
}
</script>

<style scoped>
.tab-content {
  padding: 20px;
}

.operation-buttons {
  margin-bottom: 20px;
}

.operation-buttons .el-button {
  margin-right: 10px;
}

.result-panel {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
}

.unique-key {
  margin-top: 10px;
  padding: 10px;
  background-color: #f5f7fa;
  border-radius: 4px;
}

.unique-key p {
  margin: 0;
  font-family: monospace;
}
</style>
